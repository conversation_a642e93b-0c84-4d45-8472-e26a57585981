# Build et Deploy des Custom Tasks Wexflow
# Compatible macOS et Windows

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  Build et Deploy des Custom Tasks" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Sauvegarde du répertoire courant
$OriginalDir = Get-Location

try {
    # Navigation vers le dossier des custom tasks
    $ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
    $CustomTasksDir = Join-Path $ScriptDir "wexflow-lib-custom-task"
    
    Write-Host "Navigation vers: $CustomTasksDir" -ForegroundColor Yellow
    Set-Location $CustomTasksDir

    # Vérification que le fichier solution existe
    if (-not (Test-Path "WEXFLOW.sln")) {
        Write-Host "ERREUR: Fichier solution WEXFLOW.sln non trouvé !" -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }

    # Clean et build de la solution
    Write-Host ""
    Write-Host "Nettoyage des anciens builds..." -ForegroundColor Yellow
    $cleanResult = & dotnet clean WEXFLOW.sln
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERREUR lors du nettoyage !" -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }

    Write-Host ""
    Write-Host "Compilation en mode Release..." -ForegroundColor Yellow
    $buildResult = & dotnet build WEXFLOW.sln --configuration Release --no-restore
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERREUR lors de la compilation !" -ForegroundColor Red
        Read-Host "Appuyez sur Entrée pour continuer"
        exit 1
    }

    # Création des dossiers de destination s'ils n'existent pas
    $DestDir = Join-Path $ScriptDir "wexflow-8.9-windows-netcore\Wexflow.Custom"
    $TasksDir = Join-Path $ScriptDir "wexflow-8.9-windows-netcore\Wexflow-netcore\Tasks"
    
    if (-not (Test-Path $DestDir)) {
        Write-Host "Création du dossier de destination Wexflow.Custom..." -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $DestDir -Force | Out-Null
    }
    
    if (-not (Test-Path $TasksDir)) {
        Write-Host "Création du dossier de destination Tasks..." -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $TasksDir -Force | Out-Null
    }

    # Copie des DLL compilées
    Write-Host ""
    Write-Host "Copie des tâches personnalisées..." -ForegroundColor Yellow

    $Projects = @(
        @{Name = "Wexflow.Tasks.BiodataFileWatcher"; DisplayName = "BiodataFileWatcher"},
        @{Name = "Wexflow.Tasks.BiodataJobStatusUpdater"; DisplayName = "BiodataJobStatusUpdater"},
        @{Name = "Wexflow.Tasks.BiodataResultDownloader"; DisplayName = "BiodataResultDownloader"}
    )

    $CopiedCount = 0
    foreach ($Project in $Projects) {
        $SourceDir = Join-Path $Project.Name "bin\Release\net9.0"

        if (Test-Path $SourceDir) {
            # Copier la DLL principale du projet
            $MainDll = Join-Path $SourceDir "$($Project.Name).dll"
            if (Test-Path $MainDll) {
                # Copie vers Wexflow.Custom
                Copy-Item $MainDll $DestDir -Force
                Write-Host "- $($Project.DisplayName) copiée vers Wexflow.Custom" -ForegroundColor Green

                # Copie vers Wexflow-netcore/Tasks
                Copy-Item $MainDll $TasksDir -Force
                Write-Host "- $($Project.DisplayName) copiée vers Tasks" -ForegroundColor Green

                # Copier les dépendances SQLite si le projet l'utilise
                if ($Project.Name -eq "Wexflow.Tasks.BiodataJobStatusUpdater") {
                    # Liste des DLLs SQLite à copier
                    $SqliteDependencies = @(
                        "System.Data.SQLite.dll",
                        "SQLite.Interop.dll"
                    )
                    
                    # Copier chaque dépendance
                    foreach ($dependency in $SqliteDependencies) {
                        $DllPath = Join-Path $SourceDir $dependency
                        if (Test-Path $DllPath) {
                            Copy-Item $DllPath $DestDir -Force
                            Copy-Item $DllPath $TasksDir -Force
                            Write-Host "  + $dependency copiée" -ForegroundColor Gray
                        } else {
                            Write-Host "  ATTENTION: $dependency non trouvée" -ForegroundColor Yellow
                        }
                    }
                }

                $CopiedCount++
            } else {
                Write-Host "ATTENTION: DLL principale non trouvée pour $($Project.Name)" -ForegroundColor Yellow
            }
        } else {
            Write-Host "ATTENTION: Dossier de build non trouvé pour $($Project.Name)" -ForegroundColor Yellow
        }
    }

    # Copie des workflows personnalisés
    Write-Host ""
    Write-Host "Copie des workflows personnalisés..." -ForegroundColor Yellow
    $WorkflowsSourceDir = Join-Path $ScriptDir "wexflow-worfklows"
    $WorkflowsDestDir = Join-Path $ScriptDir "wexflow-8.9-windows-netcore\Wexflow-netcore\Workflows"

    $WorkflowsCopiedCount = 0
    if (Test-Path $WorkflowsSourceDir) {
        $WorkflowFiles = Get-ChildItem $WorkflowsSourceDir -Filter "*.xml"
        foreach ($WorkflowFile in $WorkflowFiles) {
            Copy-Item $WorkflowFile.FullName $WorkflowsDestDir -Force
            Write-Host "- $($WorkflowFile.Name) copié" -ForegroundColor Green
            $WorkflowsCopiedCount++
        }
    } else {
        Write-Host "ATTENTION: Dossier des workflows non trouvé: $WorkflowsSourceDir" -ForegroundColor Yellow
    }

    # Affichage du résumé
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "  Déploiement terminé !" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Résumé:" -ForegroundColor White
    Write-Host "- $CopiedCount projet(s) déployé(s)" -ForegroundColor Green
    Write-Host "- $WorkflowsCopiedCount workflow(s) copié(s)" -ForegroundColor Green
    Write-Host "- Destination DLL 1: $DestDir" -ForegroundColor White
    Write-Host "- Destination DLL 2: $TasksDir" -ForegroundColor White
    Write-Host "- Destination Workflows: $WorkflowsDestDir" -ForegroundColor White
    Write-Host ""
    
    # Liste des fichiers copiés dans Wexflow.Custom
    $CopiedFiles = Get-ChildItem $DestDir -Filter "*.dll" | Sort-Object Name
    if ($CopiedFiles.Count -gt 0) {
        Write-Host "DLL déployées dans Wexflow.Custom:" -ForegroundColor White
        foreach ($File in $CopiedFiles) {
            Write-Host "  - $($File.Name)" -ForegroundColor Gray
        }
    }
    
    # Liste des fichiers copiés dans Tasks
    $TasksFiles = Get-ChildItem $TasksDir -Filter "*.dll" | Sort-Object Name
    if ($TasksFiles.Count -gt 0) {
        Write-Host "DLL déployées dans Tasks:" -ForegroundColor White
        foreach ($File in $TasksFiles) {
            Write-Host "  - $($File.Name)" -ForegroundColor Gray
        }
    }

    # Liste des workflows copiés
    if ($WorkflowsCopiedCount -gt 0) {
        $CopiedWorkflows = Get-ChildItem $WorkflowsDestDir -Filter "Workflow_Biodata*.xml" | Sort-Object Name
        if ($CopiedWorkflows.Count -gt 0) {
            Write-Host "Workflows copiés:" -ForegroundColor White
            foreach ($Workflow in $CopiedWorkflows) {
                Write-Host "  - $($Workflow.Name)" -ForegroundColor Gray
            }
        }
    }
    
    # Copie des workflows personnalisés
    Write-Host ""
    Write-Host "Copie des workflows personnalisés..." -ForegroundColor Yellow
    $WorkflowsSourceDir = Join-Path $ScriptDir "biodata-wexflow\wexflow-worfklows"
    $WorkflowsDestDir = Join-Path $ScriptDir "biodata-wexflow\wexflow-8.9-windows-netcore\Wexflow-netcore\Workflows"

    if (Test-Path $WorkflowsSourceDir) {
        $WorkflowFiles = Get-ChildItem $WorkflowsSourceDir -Filter "*.xml"
        foreach ($WorkflowFile in $WorkflowFiles) {
            Copy-Item $WorkflowFile.FullName $WorkflowsDestDir -Force
            Write-Host "- $($WorkflowFile.Name) copié" -ForegroundColor Green
        }
    }

    Write-Host ""
    Write-Host "Vous pouvez maintenant utiliser vos custom tasks dans Wexflow." -ForegroundColor Green
    Write-Host ""

} catch {
    Write-Host "ERREUR: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Retour au répertoire original
    Set-Location $OriginalDir
    Read-Host "Appuyez sur Entrée pour continuer"
}
