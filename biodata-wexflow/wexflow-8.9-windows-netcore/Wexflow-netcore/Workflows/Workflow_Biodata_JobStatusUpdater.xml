<Workflow xmlns="urn:wexflow-schema" id="197" name="Biodata Jobs Status Updater" description="Workflow de lmise à jour des status des jobs">
  <Settings>
    <Setting name="launchType" value="cron" />
    <Setting name="cronExpression" value="0 0/2 * * * ?" /> <!-- Every two minutes -->
    <Setting name="enabled" value="true" />
    <Setting name="enableParallelJobs" value="false" />
  </Settings>
  <Tasks>
    <!-- 1. BiodataJobStatusUpdater : mise à jour des status des jobs-->
    <Task id="2" name="BiodataJobStatusUpdater" description="Mise à jour des statuts des jobs biodata via l'API Thema" enabled="true">
      <Setting name="themaApiUrl" value="http://localhost:5066" />
      <Setting name="activeStatuses" value="SUBMITTED,RUNNING,RUNNABLE,STARTING" />
      <Setting name="sqlitePath" value="../api/Db/biodata_trace.db" />
    </Task>
  </Tasks>
  <ExecutionGraph>
    <Task id="2"><Parent id="-1" /></Task>
  </ExecutionGraph> 
</Workflow>
